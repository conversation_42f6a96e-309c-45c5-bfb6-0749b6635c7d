import React from 'react';

interface GreetingThumbnailProps {
  handleDragStart?: (event: React.DragEvent) => void;
  enableDrag?: boolean;
}

export const GreetingThumbnail: React.FC<GreetingThumbnailProps> = ({ handleDragStart, enableDrag }) => {
  return (
    <div
      className="widget-thumbnail greeting-thumbnail"
      draggable={enableDrag}
      onDragStart={handleDragStart}
    >
      <div className="thumbnail-header">
        <span className="thumbnail-icon">👋</span>
        <span className="thumbnail-title">Greeting</span>
      </div>
      <div className="thumbnail-content">
        <div className="greeting-display">
          <div className="greeting-text">Hello, Developer!</div>
          <div className="greeting-input">
            <input
              type="text"
              placeholder="Your name..."
              value="Developer"
              readOnly
              className="greeting-field"
            />
          </div>
        </div>
      </div>
    </div>
  );
};
