import React from 'react';

interface TodoThumbnailProps {
  handleDragStart?: (event: React.DragEvent) => void;
  enableDrag?: boolean;
}

export const TodoThumbnail: React.FC<TodoThumbnailProps> = ({ handleDragStart, enableDrag }) => {
  return (
    <div
      className="widget-thumbnail todo-thumbnail"
      draggable={enableDrag}
      onDragStart={handleDragStart}
    >
      <div className="thumbnail-header">
        <span className="thumbnail-icon">📝</span>
        <span className="thumbnail-title">Todo List</span>
      </div>
      <div className="thumbnail-content">
        <div className="todo-display">
          <div className="todo-input">
            <input
              type="text"
              placeholder="Add new task..."
              readOnly
              className="todo-field"
            />
          </div>
          <div className="todo-items">
            <div className="todo-item completed">
              <span className="todo-checkbox">✓</span>
              <span className="todo-text">Learn React</span>
            </div>
            <div className="todo-item">
              <span className="todo-checkbox">☐</span>
              <span className="todo-text">Build widgets</span>
            </div>
            <div className="todo-item">
              <span className="todo-checkbox">☐</span>
              <span className="todo-text">Deploy app</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
