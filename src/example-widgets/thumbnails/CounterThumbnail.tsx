import React from 'react';

interface CounterThumbnailProps {
  handleDragStart?: (event: React.DragEvent) => void;
  enableDrag?: boolean;
}

export const CounterThumbnail: React.FC<CounterThumbnailProps> = ({ handleDragStart, enableDrag }) => {
  return (
    <div
      className="widget-thumbnail counter-thumbnail"
      draggable={enableDrag}
      onDragStart={handleDragStart}
    >
      <div className="thumbnail-header">
        <span className="thumbnail-icon">🔢</span>
        <span className="thumbnail-title">Counter</span>
      </div>
      <div className="thumbnail-content">
        <div className="counter-display">
          <div className="counter-value">42</div>
          <div className="counter-buttons">
            <button className="counter-btn minus">−</button>
            <button className="counter-btn plus">+</button>
          </div>
        </div>
      </div>
    </div>
  );
};
