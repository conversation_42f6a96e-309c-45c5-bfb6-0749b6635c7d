import React from "react";
import { useWidgets } from "./useWidgets";
import type { Widget } from "./types";

interface WidgetsCatalogProps {
  className?: string;
  widgets?: Widget[];
  onWidgetSelect?: (widget: Widget) => void;
  onDrag?: (widget: Widget) => void;
  renderWidget?: (widget: Widget) => React.ReactNode;
  enableDrag?: boolean;
}

export const WidgetsCatalog: React.FC<WidgetsCatalogProps> = ({
  className = "",
  widgets: propWidgets,
  onWidgetSelect,
  onDrag,
  renderWidget,
  enableDrag = false,
}) => {
  const { widgets: contextWidgets } = useWidgets();
  const widgets = propWidgets || contextWidgets;

  const handleWidgetClick = (widget: Widget) => {
    if (onWidgetSelect) {
      onWidgetSelect(widget);
    }
  };

  const handleDragStart = (widget: Widget, event: React.DragEvent) => {
    if (enableDrag) {
      // Only serialize safe properties (exclude component and thumbnail)
      const { ...serializableWidget } = widget;
      event.dataTransfer.setData(
        "application/json",
        JSON.stringify({
          widget: serializableWidget,
          type: "widget",
        })
      );
      event.dataTransfer.effectAllowed = "copy";

      if (onDrag) {
        onDrag(widget);
      }
    }
  };

  const defaultRenderWidget = (widget: Widget) => {
    if (typeof widget.thumbnail === "function") {
      // Pass handleDragStart and enableDrag as props to the thumbnail
      return React.createElement(widget.thumbnail, {
        handleDragStart: (e: React.DragEvent) => handleDragStart(widget, e),
        enableDrag,
        key: widget.id,
      });
    }
    return (
      <div
        key={widget.id}
        className={`widget-card ${enableDrag ? "draggable" : ""}`}
        draggable={enableDrag}
        onClick={() => handleWidgetClick(widget)}
        onDragStart={(e) => handleDragStart(widget, e)}
      >
        <h3>{widget.name}</h3>
        {widget.description && <p>{widget.description}</p>}
        {widget.tags && widget.tags.length > 0 && (
          <div className="widget-tags">
            {widget.tags.map((tag, index) => (
              <span key={index} className="widget-tag">
                {tag}
              </span>
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`widgets-catalog ${className}`}>
      <div className="widgets-grid">
        {widgets.length > 0 ? (
          widgets.map((widget) =>
            renderWidget ? renderWidget(widget) : defaultRenderWidget(widget)
          )
        ) : (
          <div className="no-widgets">No widgets found</div>
        )}
      </div>
    </div>
  );
};

export default WidgetsCatalog;
