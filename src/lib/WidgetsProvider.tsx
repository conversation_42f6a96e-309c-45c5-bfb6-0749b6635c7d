import React, { useState } from 'react';
import type { Widget } from './types';
import { WidgetsContext, type WidgetsContextType } from './useWidgets';

interface WidgetsProviderProps {
  children: React.ReactNode;
  initialWidgets?: Widget[];
}


export const WidgetsProvider: React.FC<WidgetsProviderProps> = ({
  children,
  initialWidgets = [],
}) => {
  const [widgets, setWidgets] = useState<Widget[]>(initialWidgets);

  const addWidget = (widget: Widget) => {
    setWidgets(prev => {
      if (prev.some(w => w.id === widget.id)) {
        console.warn(`Widget with id "${widget.id}" already exists`);
        return prev;
      }
      return [...prev, widget];
    });
  };

  const removeWidget = (widgetId: string) => {
    setWidgets(prev => prev.filter(w => w.id !== widgetId));
  };

  const contextValue: WidgetsContextType = {
    widgets,
    addWidget,
    removeWidget,
  };

  return (
    <WidgetsContext.Provider value={contextValue}>
      {children}
    </WidgetsContext.Provider>
  );
};

export default WidgetsProvider;
