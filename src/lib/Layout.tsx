import React from 'react';
import { Responsive, Width<PERSON>rovider } from 'react-grid-layout';
import type { ResponsiveProps, Layout as RGLLayout } from 'react-grid-layout';

const ResponsiveGridLayout = WidthProvider(Responsive);

/**
 * Layout component that provides full access to react-grid-layout's ResponsiveGridLayout props.
 *
 * @example
 * ```tsx
 * <Layout
 *   layout={currentLayout}
 *   cols={{ lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 }}
 *   rowHeight={50}
 *   margin={[16, 16]}
 *   containerPadding={[24, 24]}
 *   isDraggable={true}
 *   isResizable={true}
 *   isDroppable={true}
 *   onLayoutChange={handleLayoutChange}
 *   onDrop={handleDrop}
 *   breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
 *   // All other react-grid-layout ResponsiveProps are available
 *   compactType="vertical"
 *   preventCollision={false}
 *   autoSize={true}
 * >
 *   {children}
 * </Layout>
 * ```
 */
interface LayoutProps extends Omit<ResponsiveProps, 'children'> {
  children?: React.ReactNode;
  className?: string;
  // Add backward compatibility for single layout prop
  layout?: RGLLayout[];
}

export const Layout: React.FC<LayoutProps> = ({
  children,
  className = '',
  layouts,
  layout,
  cols = { lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 },
  rowHeight = 1000,
  width = 1200,
  margin = [10, 10],
  containerPadding = [10, 10],
  isDraggable = true,
  isResizable = true,
  isDroppable = true,
  breakpoints = { lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 },
  droppingItem = {
    i: '__dropping-elem__',
    w: 4,
    h: 5,
  },
  ...props
}) => {
  // Use layouts prop if provided, otherwise create from layout prop
  const finalLayouts = layouts || (layout ? { lg: layout } : {});

  return (
    <div className={`layout-grid ${className}`} style={{ minHeight: '100%', height: '100%' }}>
      <ResponsiveGridLayout
        className="layout"
        layouts={finalLayouts}
        breakpoints={breakpoints}
        cols={cols}
        rowHeight={rowHeight}
        margin={margin}
        containerPadding={containerPadding}
        isDraggable={isDraggable}
        isResizable={isResizable}
        isDroppable={isDroppable}
        droppingItem={droppingItem}
        width={width}
        style={{ minHeight: '500px', height: '100%' }}
        {...props}
      >
        {children}
      </ResponsiveGridLayout>
    </div>
  );
};
