import { createContext, useContext } from 'react';
import type { Widget } from './types';

export interface WidgetsContextType {
  widgets: Widget[];
  addWidget: (widget: Widget) => void;
  removeWidget: (widgetId: string) => void;
}

export const WidgetsContext = createContext<WidgetsContextType | undefined>(undefined);

export const useWidgets = (): WidgetsContextType => {
  const context = useContext(WidgetsContext);
  if (!context) {
    throw new Error('useWidgets must be used within a WidgetsProvider');
  }
  return context;
};
