import React from 'react';

export interface Widget {
  id: string;
  name: string;
  description?: string;
  component: React.ComponentType<Record<string, unknown>>;
  thumbnail?: React.ComponentType<Record<string, unknown>>;
  props?: Record<string, unknown>;
  category?: string;
  tags?: string[];
  layout?: {
    x?: number;
    y?: number;
    w?: number;
    h?: number;
    minW?: number;
    minH?: number;
    maxW?: number;
    maxH?: number;
    static?: boolean;
    isDraggable?: boolean;
    isResizable?: boolean;
  };
}
