/* Widget Thumbnail Styles */
.widget-thumbnail {
  width: 100%;
  height: 120px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s, transform 0.2s, border-color 0.2s;
  cursor: pointer;
  padding: 16px;
  margin: 8px 0;
  user-select: none;
}

.widget-thumbnail:hover, .widget-thumbnail:focus-within {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  border-color: #80bfff;
  outline: none;
  transform: none;
}

.thumbnail-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
  background: none;
  border-bottom: none;
  min-height: unset;
  box-shadow: none;
  padding: 0;
}

.thumbnail-icon {
  font-size: 20px;
  line-height: 1;
  filter: none;
}

.thumbnail-title {
  font-size: 18px;
  font-weight: 600;
  color: #2d3a4a;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: 0.01em;
}

.thumbnail-content {
  flex: 1;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* Counter Thumbnail */
.counter-thumbnail .counter-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.counter-thumbnail .counter-value {
  font-size: 24px;
  font-weight: bold;
  color: #007bff;
  line-height: 1;
  text-shadow: none;
}

.counter-thumbnail .counter-buttons {
  display: flex;
  gap: 6px;
}

.counter-thumbnail .counter-btn {
  width: 28px;
  height: 22px;
  border: 1px solid #dee2e6;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 14px;
  font-weight: bold;
  color: #495057;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  transition: background 0.15s, border-color 0.15s, box-shadow 0.15s;
  box-shadow: none;
}

.counter-thumbnail .counter-btn:hover, .counter-thumbnail .counter-btn:focus {
  background: #e3f0ff;
  border-color: #b6d4fe;
  outline: none;
}

/* Greeting Thumbnail */
.greeting-thumbnail .greeting-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.greeting-thumbnail .greeting-text {
  font-size: 16px;
  font-weight: 600;
  color: #28a745;
  text-align: center;
  line-height: 1.2;
  text-shadow: none;
}

.greeting-thumbnail .greeting-field {
  width: 100%;
  max-width: 110px;
  height: 22px;
  padding: 3px 8px;
  border: 1px solid #ced4da;
  border-radius: 5px;
  font-size: 12px;
  text-align: center;
  background: #f8f9fa;
  color: #495057;
  transition: border-color 0.15s, box-shadow 0.15s;
}

.greeting-thumbnail .greeting-field:focus {
  border-color: #80bfff;
  box-shadow: 0 0 0 2px #e3f0ff;
  outline: none;
}

/* Todo Thumbnail */
.todo-thumbnail .todo-display {
  display: flex;
  flex-direction: column;
  gap: 6px;
  width: 100%;
  height: 100%;
}

.todo-thumbnail .todo-field {
  width: 100%;
  height: 20px;
  padding: 3px 8px;
  border: 1px solid #ced4da;
  border-radius: 5px;
  font-size: 11px;
  background: #f8f9fa;
  color: #6c757d;
  transition: border-color 0.15s, box-shadow 0.15s;
}

.todo-thumbnail .todo-field:focus {
  border-color: #80bfff;
  box-shadow: 0 0 0 2px #e3f0ff;
  outline: none;
}

.todo-thumbnail .todo-items {
  display: flex;
  flex-direction: column;
  gap: 3px;
  flex: 1;
  overflow: hidden;
}

.todo-thumbnail .todo-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 11px;
  line-height: 1.2;
  padding: 1px 0;
}

.todo-thumbnail .todo-checkbox {
  font-size: 10px;
  color: #28a745;
  width: 12px;
  text-align: center;
}

.todo-thumbnail .todo-text {
  color: #495057;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.todo-thumbnail .todo-item.completed .todo-text {
  text-decoration: line-through;
  color: #6c757d;
}

@media (max-width: 768px) {
  .widget-thumbnail {
    height: 100px;
    padding: 10px;
  }
  .thumbnail-header {
    margin-bottom: 4px;
  }
}
